-- Create "pending_community_tasks" table
CREATE TABLE "public"."pending_community_tasks" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "task_id" uuid NOT NULL,
  "status" character varying(20) NOT NULL DEFAULT 'PENDING',
  "clicked_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "completion_time" timestamptz NULL,
  "actual_completed_at" timestamptz NULL,
  "verification_data" jsonb NULL,
  "ip_address" inet NULL,
  "user_agent" text NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_pending_community_tasks_task" FOREIGN KEY ("task_id") REFERENCES "public"."activity_tasks" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_pending_community_tasks_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_pending_community_tasks_task_id" to table: "pending_community_tasks"
CREATE INDEX "idx_pending_community_tasks_task_id" ON "public"."pending_community_tasks" ("task_id");
-- Create index "idx_pending_community_tasks_user_id" to table: "pending_community_tasks"
CREATE INDEX "idx_pending_community_tasks_user_id" ON "public"."pending_community_tasks" ("user_id");
