package resolvers

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

func TestTimestampIntegration(t *testing.T) {
	// Setup test configuration
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	// Test data
	categoryID := "1"
	taskName := "Test Timestamp Task"
	taskDescription := "Testing timestamp conversion"
	points := 100

	// Create timestamps for testing
	now := time.Now()
	startTime := now.Add(-1 * time.Hour)
	endTime := now.Add(24 * time.Hour)

	// Convert to timestamps (milliseconds)
	startTimestamp := startTime.UnixMilli()
	endTimestamp := endTime.UnixMilli()

	t.Run("CreateTask with timestamp conversion", func(t *testing.T) {
		// Test input structure (for validation)
		_ = gql_model.CreateTaskInput{
			CategoryID:  categoryID,
			Name:        taskName,
			Description: &taskDescription,
			TaskType:    gql_model.TaskTypeCustom,
			Frequency:   gql_model.TaskFrequencyOnce,
			Points:      points,
			StartDate:   &startTimestamp,
			EndDate:     &endTimestamp,
		}

		// This would normally create a task, but we're testing the conversion logic
		// The actual creation might fail due to missing database setup, but we can verify
		// that the timestamp conversion logic works correctly

		// Test the conversion functions directly
		convertedStartTime := utils.TimestampToTime(&startTimestamp)
		convertedEndTime := utils.TimestampToTime(&endTimestamp)

		assert.NotNil(t, convertedStartTime)
		assert.NotNil(t, convertedEndTime)

		// Verify the conversion is accurate (within millisecond precision)
		expectedStartTime := time.UnixMilli(startTimestamp)
		expectedEndTime := time.UnixMilli(endTimestamp)

		assert.True(t, convertedStartTime.Equal(expectedStartTime))
		assert.True(t, convertedEndTime.Equal(expectedEndTime))
	})

	t.Run("UpdateTask with timestamp conversion", func(t *testing.T) {
		taskID := uuid.New().String()

		// New timestamps for update
		newStartTime := now.Add(2 * time.Hour)
		newEndTime := now.Add(48 * time.Hour)
		newStartTimestamp := newStartTime.UnixMilli()
		newEndTimestamp := newEndTime.UnixMilli()

		input := gql_model.UpdateTaskInput{
			ID:        taskID,
			StartDate: &newStartTimestamp,
			EndDate:   &newEndTimestamp,
		}

		// Test the conversion functions directly
		convertedStartTime := utils.TimestampToTime(input.StartDate)
		convertedEndTime := utils.TimestampToTime(input.EndDate)

		assert.NotNil(t, convertedStartTime)
		assert.NotNil(t, convertedEndTime)

		// Verify the conversion is accurate
		expectedStartTime := time.UnixMilli(newStartTimestamp)
		expectedEndTime := time.UnixMilli(newEndTimestamp)

		assert.True(t, convertedStartTime.Equal(expectedStartTime))
		assert.True(t, convertedEndTime.Equal(expectedEndTime))
	})

	t.Run("ConvertActivityTaskToGQL with timestamp conversion", func(t *testing.T) {
		// Create a model task with time.Time fields
		task := &model.ActivityTask{
			ID:          uuid.New(),
			CategoryID:  1,
			Name:        taskName,
			Description: &taskDescription,
			TaskType:    model.TaskType("CUSTOM"),
			Frequency:   model.TaskFrequency("ONCE"),
			Points:      points,
			StartDate:   &startTime,
			EndDate:     &endTime,
			IsActive:    true,
			SortOrder:   0,
			CreatedAt:   now,
			UpdatedAt:   now,
		}

		// Convert to GraphQL model
		gqlTask := convertActivityTaskToGQL(task)

		assert.NotNil(t, gqlTask)
		assert.Equal(t, task.Name, gqlTask.Name)
		assert.Equal(t, task.Points, gqlTask.Points)

		// Verify timestamp conversion
		assert.NotNil(t, gqlTask.StartDate)
		assert.NotNil(t, gqlTask.EndDate)

		// Convert back to verify accuracy
		convertedStartTime := utils.TimestampToTime(gqlTask.StartDate)
		convertedEndTime := utils.TimestampToTime(gqlTask.EndDate)

		assert.NotNil(t, convertedStartTime)
		assert.NotNil(t, convertedEndTime)

		// Verify the round-trip conversion (with millisecond precision)
		expectedStartTime := time.UnixMilli(startTime.UnixMilli())
		expectedEndTime := time.UnixMilli(endTime.UnixMilli())

		assert.True(t, convertedStartTime.Equal(expectedStartTime))
		assert.True(t, convertedEndTime.Equal(expectedEndTime))
	})

	t.Run("Nil timestamp handling", func(t *testing.T) {
		// Test with nil timestamps
		task := &model.ActivityTask{
			ID:         uuid.New(),
			CategoryID: 1,
			Name:       taskName,
			TaskType:   model.TaskType("CUSTOM"),
			Frequency:  model.TaskFrequency("ONCE"),
			Points:     points,
			StartDate:  nil, // nil timestamp
			EndDate:    nil, // nil timestamp
			IsActive:   true,
			SortOrder:  0,
			CreatedAt:  now,
			UpdatedAt:  now,
		}

		// Convert to GraphQL model
		gqlTask := convertActivityTaskToGQL(task)

		assert.NotNil(t, gqlTask)
		assert.Nil(t, gqlTask.StartDate)
		assert.Nil(t, gqlTask.EndDate)
	})
}

func TestTimestampValidation(t *testing.T) {
	t.Run("Valid timestamp range", func(t *testing.T) {
		now := time.Now()
		validTimestamp := now.UnixMilli()

		assert.True(t, utils.ValidateTimestamp(&validTimestamp))
	})

	t.Run("Invalid timestamp - too old", func(t *testing.T) {
		oldTime := time.Date(1999, 1, 1, 0, 0, 0, 0, time.UTC)
		oldTimestamp := oldTime.UnixMilli()

		assert.False(t, utils.ValidateTimestamp(&oldTimestamp))
	})

	t.Run("Invalid timestamp - too far in future", func(t *testing.T) {
		futureTime := time.Now().AddDate(15, 0, 0) // 15 years in future
		futureTimestamp := futureTime.UnixMilli()

		assert.False(t, utils.ValidateTimestamp(&futureTimestamp))
	})

	t.Run("Nil timestamp is valid", func(t *testing.T) {
		assert.True(t, utils.ValidateTimestamp(nil))
	})
}
