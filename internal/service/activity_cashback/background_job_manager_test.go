package activity_cashback

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"go.uber.org/zap"
)

func TestBackgroundJobManager_StartStop(t *testing.T) {
	// Initialize logger for testing
	if global.GVA_LOG == nil {
		logger, _ := zap.NewDevelopment()
		global.GVA_LOG = logger
	}

	bjm := NewBackgroundJobManager()

	t.Run("Background job manager can start", func(t *testing.T) {
		err := bjm.Start()
		require.NoError(t, err)
		assert.True(t, bjm.IsRunning())

		// Get job status
		status := bjm.GetJobStatus()
		assert.NotNil(t, status)
		assert.Equal(t, true, status["is_running"])

		// Check that jobs are listed
		jobs, ok := status["jobs"].([]map[string]interface{})
		assert.True(t, ok)
		assert.Greater(t, len(jobs), 0)

		// Look for pending community tasks job
		foundPendingJob := false
		for _, job := range jobs {
			if job["name"] == "pending_community_tasks" {
				foundPendingJob = true
				assert.Equal(t, "Processes pending community tasks after 2 minutes", job["description"])
				assert.Equal(t, "1 minute", job["interval"])
				break
			}
		}
		assert.True(t, foundPendingJob, "Should find pending_community_tasks job in status")
	})

	t.Run("Background job manager can stop", func(t *testing.T) {
		// Let it run for a short time
		time.Sleep(100 * time.Millisecond)

		bjm.Stop()
		assert.False(t, bjm.IsRunning())
	})

	t.Run("Cannot start already running manager", func(t *testing.T) {
		// Start again
		err := bjm.Start()
		require.NoError(t, err)
		assert.True(t, bjm.IsRunning())

		// Try to start again - should not error but should warn
		err = bjm.Start()
		require.NoError(t, err)
		assert.True(t, bjm.IsRunning())

		// Clean up
		bjm.Stop()
	})
}

func TestBackgroundJobManager_ProcessPendingCommunityTasks(t *testing.T) {
	bjm := NewBackgroundJobManager()

	t.Run("processPendingCommunityTasks does not panic", func(t *testing.T) {
		// This should not panic even without database connection
		// It should just log an error
		assert.NotPanics(t, func() {
			bjm.processPendingCommunityTasks()
		})
	})
}
