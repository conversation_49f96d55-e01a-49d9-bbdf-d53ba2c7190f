package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRequiresTwoMinuteWait(t *testing.T) {
	tests := []struct {
		name     string
		taskID   TaskIdentifier
		expected bool
	}{
		{
			name:     "Twitter Follow requires 2-minute wait",
			taskID:   TaskIDTwitterFollow,
			expected: true,
		},
		{
			name:     "Twitter Retweet requires 2-minute wait",
			taskID:   TaskIDTwitterRetweet,
			expected: true,
		},
		{
			name:     "Twitter Like requires 2-minute wait",
			taskID:   TaskIDTwitterLike,
			expected: true,
		},
		{
			name:     "Telegram Join requires 2-minute wait",
			taskID:   TaskIDTelegramJoin,
			expected: true,
		},
		{
			name:     "Invite Friends does not require 2-minute wait",
			taskID:   TaskIDInviteFriends,
			expected: false,
		},
		{
			name:     "Share Referral does not require 2-minute wait",
			taskID:   TaskIDShareReferral,
			expected: false,
		},
		{
			name:     "Daily Checkin does not require 2-minute wait",
			taskID:   TaskIDDailyCheckin,
			expected: false,
		},
		{
			name:     "Trading Points does not require 2-minute wait",
			taskID:   TaskIDTradingPoints,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := RequiresTwoMinuteWait(tt.taskID)
			assert.Equal(t, tt.expected, result, "RequiresTwoMinuteWait(%s) should return %v", tt.taskID, tt.expected)
		})
	}
}

func TestCommunityTasksRequiring2MinuteWait(t *testing.T) {
	// Test that the slice contains exactly the expected tasks
	expected := []TaskIdentifier{
		TaskIDTwitterFollow,
		TaskIDTwitterRetweet,
		TaskIDTwitterLike,
		TaskIDTelegramJoin,
	}

	assert.Equal(t, expected, CommunityTasksRequiring2MinuteWait, "CommunityTasksRequiring2MinuteWait should contain exactly the expected tasks")
	assert.Len(t, CommunityTasksRequiring2MinuteWait, 4, "Should have exactly 4 tasks requiring 2-minute wait")
}
