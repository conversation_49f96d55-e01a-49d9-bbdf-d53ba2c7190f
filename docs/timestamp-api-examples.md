# Timestamp API Examples

<PERSON><PERSON> <PERSON>hi thay đổi từ Time sang Int64 timestamp, đ<PERSON><PERSON> là cách sử dụng API với Postman:

## Format Timestamp

- **Timestamp format**: Int64 (milliseconds since Unix epoch)
- **Ví dụ**: `1756972187000` = 2025-09-04 15:23:07 UTC

## Tạo Timestamp từ Date

### JavaScript (Browser Console)
```javascript
// Tạo timestamp cho ngày hiện tại
new Date().getTime()

// Tạo timestamp cho ngày cụ thể
new Date('2024-12-31T23:59:59Z').getTime()  // 1735689599000
new Date('2024-01-15T10:30:00Z').getTime()  // 1705315800000
```

### Python
```python
import time
from datetime import datetime

# Timestamp hiện tại
int(time.time() * 1000)

# Timestamp cho ngày cụ thể
int(datetime(2024, 12, 31, 23, 59, 59).timestamp() * 1000)
```

## API Examples cho Postman

### 1. Create Task (Admin API)

**Endpoint**: `POST /admin/graphql`

**Headers**:
```
Content-Type: application/json
Authorization: Bearer <admin-token>
```

**Body**:
```json
{
  "query": "mutation CreateTask($input: CreateTaskInput!) { createTask(input: $input) { id name startDate endDate } }",
  "variables": {
    "input": {
      "categoryId": "1",
      "name": "Test Task with Timestamp",
      "description": "Testing new timestamp format",
      "taskType": "CUSTOM",
      "frequency": "ONCE",
      "points": 100,
      "startDate": 1705315800000,
      "endDate": 1735689599000
    }
  }
}
```

### 2. Update Task (Admin API)

**Endpoint**: `POST /admin/graphql`

**Body**:
```json
{
  "query": "mutation UpdateTask($input: UpdateTaskInput!) { updateTask(input: $input) { id name startDate endDate } }",
  "variables": {
    "input": {
      "id": "task-uuid-here",
      "startDate": 1705315800000,
      "endDate": 1735689599000
    }
  }
}
```

### 3. Get Tasks (User API)

**Endpoint**: `POST /graphql`

**Headers**:
```
Content-Type: application/json
Authorization: Bearer <user-token>
```

**Body**:
```json
{
  "query": "query GetTaskCenter { getTaskCenter { categories { tasks { id name startDate endDate } } } }"
}
```

**Response Example**:
```json
{
  "data": {
    "getTaskCenter": {
      "categories": [
        {
          "tasks": [
            {
              "id": "task-uuid",
              "name": "Test Task",
              "startDate": 1705315800000,
              "endDate": 1735689599000
            }
          ]
        }
      ]
    }
  }
}
```

## Validation Rules

- **Minimum**: Timestamp không được nhỏ hơn 1 Jan 2000 (************)
- **Maximum**: Timestamp không được lớn hơn 10 năm từ hiện tại
- **Null values**: Được phép cho optional dates

## Migration Notes

### Trước đây (Time format):
```json
{
  "startDate": "2024-01-15T10:30:00Z",
  "endDate": "2024-12-31T23:59:59Z"
}
```

### Bây giờ (Int64 timestamp):
```json
{
  "startDate": 1705315800000,
  "endDate": 1735689599000
}
```

## Utility Functions Available

Trong code, có các utility functions để convert:

```go
// Convert time.Time to timestamp
utils.TimeToTimestamp(&timeValue) // returns *int64

// Convert timestamp to time.Time  
utils.TimestampToTime(&timestampValue) // returns *time.Time

// Validate timestamp
utils.ValidateTimestamp(&timestampValue) // returns bool
```

## Testing Checklist

- [ ] Create task với timestamp values
- [ ] Update task với timestamp values  
- [ ] Get tasks và verify timestamp format trong response
- [ ] Test với null values
- [ ] Test validation với invalid timestamps (quá cũ/quá mới)
